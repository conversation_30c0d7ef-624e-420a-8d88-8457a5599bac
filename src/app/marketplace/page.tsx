import MarketplaceHeader from "@/app/marketplace/marketplace-header";
import MarketplaceFooter from "@/app/marketplace/marketplace-footer";
import BackyardLottie from "@/components/BackyardLottie";

export default function MarketplacePage() {
  return (
    <div className="min-h-screen bg-slate-900 flex flex-col">
      <MarketplaceHeader />

      <main className="flex-1 p-4">
        <div className="max-w-6xl mx-auto">
          <div className="text-center py-20">
            <div className="mb-8 flex justify-center">
              <BackyardLottie width={300} height={300} className="rounded-lg" />
            </div>
            <h2 className="text-2xl font-bold text-white mb-4">
              No gifts found with the filters you selected
            </h2>
            <p className="text-gray-400">
              Try adjusting your filters or browse all available items.
            </p>
          </div>
        </div>
      </main>

      <MarketplaceFooter />
    </div>
  );
}
