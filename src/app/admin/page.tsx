"use client";

import { useEffect } from "react";

import { Role } from "@/core.constants";
import { useRootContext } from "@/root-context";
import { CollectionManagement } from "./collection-management";
import { FeesManagement } from "./fees-management";
import { RevenueDisplay } from "./revenue-display";

export default function Admin() {
  const { role } = useRootContext();

  useEffect(() => {
    document.documentElement.classList.add("dark");
  }, []);

  if (role !== Role.ADMIN) return null;

  return (
    <div className="mx-auto w-full max-w-6xl p-4 space-y-6">
      <RevenueDisplay />

      <FeesManagement />

      <CollectionManagement />
    </div>
  );
}
