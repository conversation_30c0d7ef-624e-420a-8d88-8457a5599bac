import { doc, getDoc } from "firebase/firestore";
import { firestore } from "@/root-context";

const MARKETPLACE_REVENUE_USER_ID = "marketplace_revenue";

export interface MarketplaceRevenue {
  sum: number;
  locked: number;
}

export const getMarketplaceRevenue = async (): Promise<MarketplaceRevenue> => {
  try {
    const revenueDoc = await getDoc(
      doc(firestore, "users", MARKETPLACE_REVENUE_USER_ID)
    );

    if (revenueDoc.exists()) {
      const data = revenueDoc.data();
      return data.balance || { sum: 0, locked: 0 };
    }

    return { sum: 0, locked: 0 };
  } catch (error) {
    console.error("Error loading marketplace revenue:", error);
    throw error;
  }
};
