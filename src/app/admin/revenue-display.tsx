"use client";

import { useState, useEffect } from "react";
import { getMarketplaceRevenue, MarketplaceRevenue } from "@/api/revenue-api";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Loader2, TrendingUp } from "lucide-react";

export const RevenueDisplay = () => {
  const [revenue, setRevenue] = useState<MarketplaceRevenue | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadRevenue = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const revenueData = await getMarketplaceRevenue();
        setRevenue(revenueData);
      } catch (err) {
        console.error("Error loading revenue:", err);
        setError("Failed to load revenue data");
      } finally {
        setIsLoading(false);
      }
    };

    loadRevenue();
  }, []);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Marketplace Revenue
          </CardTitle>
          <CardDescription>
            Total revenue collected from marketplace fees
          </CardDescription>
        </CardHeader>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading revenue data...</span>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Marketplace Revenue
          </CardTitle>
          <CardDescription>
            Total revenue collected from marketplace fees
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-red-600 text-sm">{error}</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <TrendingUp className="h-5 w-5" />
          Marketplace Revenue
        </CardTitle>
        <CardDescription>
          Total revenue collected from marketplace fees
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="text-sm font-medium">Total Revenue:</span>
            <span className="text-2xl font-bold">
              {revenue?.sum.toFixed(4)} TON
            </span>
          </div>
          {revenue && revenue.locked > 0 && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-muted-foreground">Locked:</span>
              <span>{revenue.locked.toFixed(4)} TON</span>
            </div>
          )}
          <div className="flex items-center justify-between text-sm">
            <span className="text-muted-foreground">Available:</span>
            <span>
              {revenue ? (revenue.sum - revenue.locked).toFixed(4) : "0.0000"} TON
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
